import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
import _createClass from "@babel/runtime/helpers/esm/createClass";
import _assertThisInitialized from "@babel/runtime/helpers/esm/assertThisInitialized";
import _inherits from "@babel/runtime/helpers/esm/inherits";
import _createSuper from "@babel/runtime/helpers/esm/createSuper";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import AbstractCalculator from "./calculator";
var NumCalculator = /*#__PURE__*/function (_AbstractCalculator) {
  _inherits(NumCalculator, _AbstractCalculator);
  var _super = _createSuper(NumCalculator);
  function NumCalculator(num) {
    var _this;
    _classCallCheck(this, NumCalculator);
    _this = _super.call(this);
    _defineProperty(_assertThisInitialized(_this), "result", 0);
    if (num instanceof NumCalculator) {
      _this.result = num.result;
    } else if (typeof num === 'number') {
      _this.result = num;
    }
    return _this;
  }
  _createClass(NumCalculator, [{
    key: "add",
    value: function add(num) {
      if (num instanceof NumCalculator) {
        this.result += num.result;
      } else if (typeof num === 'number') {
        this.result += num;
      }
      return this;
    }
  }, {
    key: "sub",
    value: function sub(num) {
      if (num instanceof NumCalculator) {
        this.result -= num.result;
      } else if (typeof num === 'number') {
        this.result -= num;
      }
      return this;
    }
  }, {
    key: "mul",
    value: function mul(num) {
      if (num instanceof NumCalculator) {
        this.result *= num.result;
      } else if (typeof num === 'number') {
        this.result *= num;
      }
      return this;
    }
  }, {
    key: "div",
    value: function div(num) {
      if (num instanceof NumCalculator) {
        this.result /= num.result;
      } else if (typeof num === 'number') {
        this.result /= num;
      }
      return this;
    }
  }, {
    key: "equal",
    value: function equal() {
      return this.result;
    }
  }]);
  return NumCalculator;
}(AbstractCalculator);
export { NumCalculator as default };