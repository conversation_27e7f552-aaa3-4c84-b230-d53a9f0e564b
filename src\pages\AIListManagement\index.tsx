import React, { useState, useRef, useEffect } from 'react'
import { ConfigProvider, Button, Switch, Layout, Table, Modal, Input, Tree, message, Steps, Select, Progress } from 'antd'
import type { TreeProps } from 'antd'
import { CloseOutlined } from '@ant-design/icons'
import Draggable from 'react-draggable'
import { useThemeStore, getAntdTheme } from '@/stores/theme'
import * as XLSX from 'xlsx'
import * as echarts from 'echarts'

// 添加全局滚动条样式
const addScrollbarStyles = () => {
  const styleId = 'custom-scrollbar-styles';
  if (document.getElementById(styleId)) return;

  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    /* 浅色模式滚动条样式 */
    .custom-scrollbar-light {
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    }
    .custom-scrollbar-light::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    .custom-scrollbar-light::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px;
    }
    .custom-scrollbar-light::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      transition: background 0.2s ease;
    }
    .custom-scrollbar-light::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.3);
    }

    /* 深色模式滚动条样式 */
    .custom-scrollbar-dark {
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }
    .custom-scrollbar-dark::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    .custom-scrollbar-dark::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px;
    }
    .custom-scrollbar-dark::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      transition: background 0.2s ease;
    }
    .custom-scrollbar-dark::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.4);
    }

    /* 通用树容器滚动条样式 */
    .tree-scroll-container {
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }

    /* 强制显示滚动条 */
    .force-scrollbar {
      overflow-y: scroll !important;
      overflow-x: hidden !important;
    }

    /* 强制显示滚动条 - webkit浏览器 */
    .force-scrollbar::-webkit-scrollbar {
      width: 8px !important;
      background: transparent !important;
    }
    .force-scrollbar::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3) !important;
      border-radius: 4px !important;
      transition: background 0.2s ease !important;
    }
    .force-scrollbar::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.5) !important;
    }

    /* 深色模式下的滚动条样式 */
    .force-scrollbar.dark-mode::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3) !important;
    }
    .force-scrollbar.dark-mode::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5) !important;
    }
  `;
  document.head.appendChild(style);
};

const { Header, Sider, Content } = Layout;

type AlignType = 'left' | 'center' | 'right';

// 创建带有滚动条样式的容器组件
const ScrollableTreeContainer: React.FC<{
  children: React.ReactNode;
  style?: React.CSSProperties;
  darkMode: boolean;
  className?: string;
}> = ({ children, style, darkMode, className }) => {
  const scrollbarClass = darkMode ? 'custom-scrollbar-dark' : 'custom-scrollbar-light';

  // 内联样式作为备用方案
  const inlineScrollbarStyle: React.CSSProperties = {
    overflowY: 'auto',
    overflowX: 'hidden',
    scrollbarWidth: 'thin' as any,
    scrollbarColor: darkMode
      ? 'rgba(255, 255, 255, 0.3) transparent'
      : 'rgba(0, 0, 0, 0.2) transparent',
  };

  return (
    <div
      className={`${scrollbarClass} ${className || ''}`}
      style={{
        ...inlineScrollbarStyle,
        ...style
      }}
    >
      {children}
    </div>
  );
};



const treeData = [
  {
    title: '考点&考场信息',
    key: 'root',
    children: [
      {
        title: '2024年普通高等学校招生考试',
        key: '1',
        children: []
      },
      {
        title: '2024年研究生考试',
        key: '2',
        children: []
      },
      {
        title: '2024年高等教育自学考试',
        key: '3',
        children: []
      },
      {
        title: '2024年成人高等学校招生考试',
        key: '4',
        children: []
      },
      {
        title: '2024年英语四级考试',
        key: '5',
        children: []
      },
      {
        title: '2024年初中学业水平考试',
        key: '6',
        children: []
      }
    ]
  }
];

const tableData = [
  {
    key: 1,
    name: '2024年普通高等学校招生考试',
    plan: '考场编排方案1',
    time: '2024-09-06 16:40:26',
    remark: '考务导入的2024年考点信息',
    show: true
  },
  {
    key: 2,
    name: '2024年研究生考试',
    plan: '考场编排方案1',
    time: '2024-09-05 12:35:20',
    remark: '-',
    show: false
  },
  {
    key: 3,
    name: '2024年高等教育自学考试',
    plan: '考场编排方案1',
    time: '2024-09-03 02:28:59',
    remark: '-',
    show: false
  },
  {
    key: 4,
    name: '2024年成人高等学校招生考试',
    plan: '考场编排方案1',
    time: '2024-08-28 09:01:21',
    remark: '-',
    show: false
  },
  {
    key: 5,
    name: '2024年英语四级考试',
    plan: '考场编排方案1',
    time: '2024-08-27 02:03:44',
    remark: '-',
    show: false
  },
  {
    key: 6,
    name: '2024年初中学业水平考试',
    plan: '考场编排方案1',
    time: '2024-08-26 17:32:11',
    remark: '-',
    show: false
  },
  {
    key: 7,
    name: '2024年初中学业水平考试',
    plan: '考场编排方案1',
    time: '2024-08-26 07:21:26',
    remark: '-',
    show: false
  },
  {
    key: 8,
    name: '2024年初中学业水平考试',
    plan: '考场编排方案1',
    time: '2024-08-26 04:51:26',
    remark: '-',
    show: false
  },
  {
    key: 9,
    name: '2024年初中学业水平考试',
    plan: '考场编排方案1',
    time: '2024-08-24 16:35:08',
    remark: '-',
    show: false
  },
  {
    key: 10,
    name: '2024年初中学业水平考试',
    plan: '考场编排方案1',
    time: '2024-08-18 12:37:38',
    remark: '-',
    show: false
  }
];

// 标准考点&考场方案树数据
const standardTreeData = [
  {
    title: '标准考点&考场方案',
    key: 'root',
    children: [
      { title: '2024年普通高等学校招生考试', key: '2024-gaokao' },
      { title: '2024年研究生考试', key: '2024-yjs' },
      { title: '2024年高等教育自学考试', key: '2024-zikao' },
      { title: '2024年成人高等学校招生考试', key: '2024-crgk' },
      { title: '2024年英语四级考试', key: '2024-cet4' },
      { title: '2024年初中学业水平考试', key: '2024-czks' }
    ]
  }
];

// 所有考点方案数据
const allSiteSchemes = [
  { key: '2024-gaokao', name: '2024年普通高等学校招生考试', time: '2024-09-06 16:40:26', remark: '考务导入的2024年考点信息', show: true },
  { key: '2024-yjs', name: '2024年研究生考试', time: '2024-09-05 12:35:20', remark: '-', show: false },
  { key: '2024-zikao', name: '2024年高等教育自学考试', time: '2024-09-03 02:28:59', remark: '-', show: false },
  { key: '2024-crgk', name: '2024年成人高等学校招生考试', time: '2024-08-28 09:01:21', remark: '-', show: false },
  { key: '2024-cet4', name: '2024年英语四级考试', time: '2024-08-27 02:03:44', remark: '-', show: false },
  { key: '2024-czks', name: '2024年初中学业水平考试', time: '2024-08-26 17:32:11', remark: '-', show: false }
];

// 某考点下的考场方案数据
const sitePlanData = [
  { key: 1, type: 'imported', plan: '考场编排方案1', time: '2024-09-06 16:40:26', remark: '考务导入的2024年考点信息', show: true },
  { key: 1001, type: 'custom', plan: '考场编排方案2', time: '2024-09-05 12:35:20', remark: '-', show: false }
];

// 用于脏数据清理和拉流清理步骤的树数据（不包含通道）
const basicCleanupTreeData = [{
  title: '国家教育部', key: 'root', children: [
    { title: '四川省', key: 'sichuan', children: [
      { title: '成都市', key: 'chengdu', children: [
        {
          title: '锦江区',
          key: 'jinjiang',
          children: [
            { title: '树德光华中学', key: 'sdgh' },
            { title: '锦江区第一中学', key: 'jinjiang-school1' },
            { title: '锦江区实验学校', key: 'jinjiang-school2' }
          ]
        },
        {
          title: '青羊区',
          key: 'qingyang',
          children: [
            { title: '青羊区实验中学', key: 'qingyang-school1' },
            { title: '青羊区第二中学', key: 'qingyang-school2' }
          ]
        },
        {
          title: '金牛区',
          key: 'jinniu',
          children: [
            { title: '锦州医科大学医疗学院', key: 'jzyd' },
            { title: '兴义民族师范学院', key: 'xymz' },
            { title: '衡阳师范学院', key: 'hysz' },
            { title: '天津职业技术师范大学', key: 'tjzy' },
            { title: '金牛区重点中学', key: 'jinniu-school1' }
          ]
        }
      ]},
      { title: '绵阳市', key: 'mianyang', children: [
        { title: '涪城区', key: 'fuchen', children: [
          { title: '绵阳中学', key: 'mianyang-school1' },
          { title: '绵阳实验高中', key: 'mianyang-school2' }
        ]},
        { title: '游仙区', key: 'youxian', children: [
          { title: '游仙区第一中学', key: 'youxian-school1' }
        ]}
      ]}
    ]},
    { title: '广东省', key: 'guangdong', children: [
      { title: '广州市', key: 'guangzhou', children: [
        { title: '天河区', key: 'tianhe', children: [
          { title: '华南师范大学附属中学', key: 'huanan-school1' }
        ]}
      ]}
    ]},
    { title: '江苏省', key: 'jiangsu', children: [
      { title: '南京市', key: 'nanjing', children: [
        { title: '鼓楼区', key: 'gulou', children: [
          { title: '南京师范大学附属中学', key: 'nanjing-school1' }
        ]}
      ]}
    ]},
    { title: '浙江省', key: 'zhejiang', children: [
      { title: '杭州市', key: 'hangzhou', children: [
        { title: '西湖区', key: 'xihu', children: [
          { title: '杭州第二中学', key: 'hangzhou-school1' }
        ]}
      ]}
    ]}
  ]
}];

// 脏数据清理表格列定义
const dirtyDataTableColumns = [
  { title: '区县代码', dataIndex: 'code', key: 'code', width: 90 },
  { title: '机构名称', dataIndex: 'orgName', key: 'orgName', width: 140 },
  { title: '标准化考点ID', dataIndex: 'stdId', key: 'stdId', width: 140 },
  { title: '考点名称', dataIndex: 'siteName', key: 'siteName', width: 160 },
  { title: 'B考点编号', dataIndex: 'bSiteId', key: 'bSiteId', width: 100 },
  { title: '考点编号', dataIndex: 'siteId', key: 'siteId', width: 140 },
  { title: '考场编号', dataIndex: 'examRoomId', key: 'examRoomId', width: 90 },
  {
    title: '分析结果',
    key: 'analysisResult',
    width: 120,
    align: 'center' as AlignType,
    render: (_: any, record: any) => {
      if (!record.analysisResult) {
        return <span style={{ color: '#999' }}>-</span>;
      }

      const resultConfig = {
        'no-exam-site': {
          text: '无对应考点',
          color: '#ffffff',
          bgColor: '#ff4d4f',
          borderColor: '#ff4d4f'
        },
        'multi-uri': {
          text: '多URI',
          color: '#ffffff',
          bgColor: '#fa8c16',
          borderColor: '#fa8c16'
        },
        'normal': {
          text: '正常',
          color: '#ffffff',
          bgColor: '#52c41a',
          borderColor: '#52c41a'
        }
      };

      const config = resultConfig[record.analysisResult as keyof typeof resultConfig] || resultConfig.normal;

      return (
        <span style={{
          padding: '3px 10px',
          borderRadius: 12,
          fontSize: 12,
          fontWeight: 500,
          color: config.color,
          backgroundColor: config.bgColor,
          border: `1px solid ${config.borderColor}`,
          display: 'inline-block',
          minWidth: 70,
          textAlign: 'center'
        }}>
          {config.text}
        </span>
      );
    }
  }
];

// 模拟脏数据清理数据
const dirtyDataResults = Array.from({ length: 50 }, (_, i) => ({
  key: i + 1,
  code: '110108',
  orgName: '树德光华中学',
  stdId: `K110108${String(i + 1).padStart(3, '0')}`,
  siteName: '树德光华中学',
  bSiteId: '1106',
  siteId: `K110108${String(i + 1).padStart(3, '0')}`,
  examRoomId: String(i + 1).padStart(2, '0'),
  analysisResult: i % 3 === 0 ? 'no-exam-site' : i % 3 === 1 ? 'multi-uri' : 'normal'
}));

const AIListManagement: React.FC = () => {
  // 1. 所有useState声明在最前面
  const { darkMode } = useThemeStore();

  // 初始化滚动条样式
  useEffect(() => {
    addScrollbarStyles();
  }, []);

  const [modalOpen, setModalOpen] = useState(false);
  const [schemeName, setSchemeName] = useState('');
  const [remark, setRemark] = useState('');
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [siteSelectedKeys, setSiteSelectedKeys] = useState<React.Key[]>([]);
  const [orgCheckedKeys, setOrgCheckedKeys] = useState<React.Key[]>([]);
  const [siteCheckedKeys, setSiteCheckedKeys] = useState<React.Key[]>([]);
  const [selectedTreeKey, setSelectedTreeKey] = useState<string>('root');
  const [cleanupModalOpen, setCleanupModalOpen] = useState(false);
  const [recordForCleanup, setRecordForCleanup] = useState<any>(null);
  const [cleanupStep, setCleanupStep] = useState(0);
  const [selectedCleanupNode, setSelectedCleanupNode] = useState<string[]>([]);

  // 拉流相关状态
  const [streamProgressVisible, setStreamProgressVisible] = useState(false);
  const [streamProgress, setStreamProgress] = useState(0);
  const [streamProgressText, setStreamProgressText] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamStatus, setStreamStatus] = useState<Record<string, 'pending' | 'streaming' | 'success' | 'failed'>>({});
  const [completedStreamCount, setCompletedStreamCount] = useState(0);

  // 删除和导出拉流失败通道相关状态
  const [deleteFailedModalOpen, setDeleteFailedModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isExportingFailed, setIsExportingFailed] = useState(false);



  // 人工清理相关状态
  const [checkedCleanupNodes, setCheckedCleanupNodes] = useState<React.Key[]>([]);
  const [selectedManualCleanupNodes, setSelectedManualCleanupNodes] = useState<React.Key[]>([]); // 初始无选中
  const [deleteSelectedModalOpen, setDeleteSelectedModalOpen] = useState(false);
  const [isDeletingSelected, setIsDeletingSelected] = useState(false);

  // 图像查看相关状态
  const [imageViewModalOpen, setImageViewModalOpen] = useState(false);
  const [currentViewingImage, setCurrentViewingImage] = useState<any>(null);

  // 脏数据清理相关状态
  const [dirtyDataAnalyzing, setDirtyDataAnalyzing] = useState(false);
  const [dirtyDataAnalyzed, setDirtyDataAnalyzed] = useState(false);
  const [dirtyDataFilter, setDirtyDataFilter] = useState('all');
  const [dirtyDataResultsState, setDirtyDataResultsState] = useState<any[]>([]);

  // 查看和编辑相关状态
  const [viewSiteModalOpen, setViewSiteModalOpen] = useState(false);
  const [editSiteModalOpen, setEditSiteModalOpen] = useState(false);
  const [deleteSiteModalOpen, setDeleteSiteModalOpen] = useState(false);
  const [currentSiteRecord, setCurrentSiteRecord] = useState<any>(null);
  const [editSiteName, setEditSiteName] = useState('');
  const [editSiteRemark, setEditSiteRemark] = useState('');

  // States for Import Site Plan Modal
  const [importSiteModalOpen, setImportSiteModalOpen] = useState(false);
  const [importSiteName, setImportSiteName] = useState('');
  const [importSiteRemark, setImportSiteRemark] = useState('');
  const [importSiteFilePath, setImportSiteFilePath] = useState('');
  const [isImportingSite, setIsImportingSite] = useState(false);
  const [importSiteProgress, setImportSiteProgress] = useState(0);

  // States for Import Room Plan Modal
  const [importPlanModalOpen, setImportPlanModalOpen] = useState(false);
  const [importPlanName, setImportPlanName] = useState('');
  const [importPlanRemark, setImportPlanRemark] = useState('');
  const [importPlanFilePath, setImportPlanFilePath] = useState('');
  const [isImportingPlan, setIsImportingPlan] = useState(false);
  const [importPlanProgress, setImportPlanProgress] = useState(0);

  // Draggable Modal states
  const [disabled, setDisabled] = useState(true);
  const [bounds, setBounds] = useState({ left: 0, top: 0, bottom: 0, right: 0 });
  const draggleRef = useRef<HTMLDivElement>(null);
  const fileInputSiteRef = useRef<HTMLInputElement>(null);
  const fileInputPlanRef = useRef<HTMLInputElement>(null);

  // 显示状态管理
  const [siteShowMap, setSiteShowMap] = useState<Record<string, boolean>>(() => {
    const map: Record<string, boolean> = {};
    allSiteSchemes.forEach(item => {
      map[item.key] = item.show;
    });
    return map;
  });

  const [planShowMap, setPlanShowMap] = useState<Record<number, boolean>>(() => {
    const map: Record<number, boolean> = {};
    sitePlanData.forEach(item => {
      map[item.key] = item.show;
    });
    return map;
  });

  // 拖拽相关函数
  const onStart = (_event: any, uiData: any) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  // 文件选择处理
  const handleSiteFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportSiteFilePath(file.name);
    }
  };

  const handlePlanFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportPlanFilePath(file.name);
    }
  };

  // 导入考点方案处理
  const handleImportSite = async () => {
    if (!importSiteName.trim()) {
      message.error('请输入考点方案名称');
      return;
    }
    if (!importSiteFilePath) {
      message.error('请选择考场信息文件');
      return;
    }

    setIsImportingSite(true);
    setImportSiteProgress(0);

    // 模拟导入进度
    const steps = [
      { progress: 10, text: '正在读取文件...' },
      { progress: 30, text: '正在解析数据...' },
      { progress: 50, text: '正在验证格式...' },
      { progress: 70, text: '正在导入考点信息...' },
      { progress: 90, text: '正在生成机构树...' },
      { progress: 100, text: '导入完成！' }
    ];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 500));
      setImportSiteProgress(steps[i].progress);
    }

    message.success('考点方案导入成功');
    handleCloseImportSiteModal();
  };

  const handleCloseImportSiteModal = () => {
    setImportSiteModalOpen(false);
    setIsImportingSite(false);
    setImportSiteProgress(0);
    setImportSiteName('');
    setImportSiteRemark('');
    setImportSiteFilePath('');
  };

  // 导入考场方案处理
  const handleImportPlan = async () => {
    if (!importPlanName.trim()) {
      message.error('请输入考场方案名称');
      return;
    }
    if (!importPlanFilePath) {
      message.error('请选择考场信息文件');
      return;
    }

    setIsImportingPlan(true);
    setImportPlanProgress(0);

    // 模拟导入进度
    const steps = [
      { progress: 10, text: '正在读取文件...' },
      { progress: 30, text: '正在解析数据...' },
      { progress: 50, text: '正在验证格式...' },
      { progress: 70, text: '正在导入考场信息...' },
      { progress: 90, text: '正在生成考场树...' },
      { progress: 100, text: '导入完成！' }
    ];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 500));
      setImportPlanProgress(steps[i].progress);
    }

    message.success('考场方案导入成功');
    handleCloseImportPlanModal();
  };

  const handleCloseImportPlanModal = () => {
    setImportPlanModalOpen(false);
    setIsImportingPlan(false);
    setImportPlanProgress(0);
    setImportPlanName('');
    setImportPlanRemark('');
    setImportPlanFilePath('');
  };

  // 获取主题相关的样式变量
  const bgMain = darkMode ? '#18191A' : '#ffffff';
  const borderColor = darkMode ? '#434343' : '#f0f0f0';
  const colorText = darkMode ? '#ffffff' : '#000000';

  // 页面内容渲染 - 根据选中节点显示不同内容
  const isRoot = selectedTreeKey === 'root';
  let pageContent: React.ReactNode = null;

  if (isRoot) {
    // 根节点：显示标准考点&考场方案的完整功能页面（类似JF-CIA项目）
    pageContent = (
      <div style={{ display: 'flex', gap: 16, height: 'calc(100vh - 140px)' }}>
        {/* 左侧：实时通道列表 */}
        <div style={{ width: 400, display: 'flex', flexDirection: 'column' }}>
          <div style={{
            background: darkMode ? '#1e293b' : '#ffffff',
            border: `1px solid ${borderColor}`,
            borderRadius: 8,
            padding: 16,
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: 16,
              paddingBottom: 12,
              borderBottom: `1px solid ${borderColor}`
            }}>
              <span style={{ fontSize: 16, fontWeight: 'bold', color: colorText }}>实时通道列表</span>
            </div>
            <ScrollableTreeContainer
              darkMode={darkMode}
              style={{ flex: 1, height: 'calc(100% - 60px)' }}
            >
              <Tree
                treeData={basicCleanupTreeData}
                checkable
                defaultExpandAll
                checkedKeys={orgCheckedKeys}
                onCheck={(checkedKeys) => setOrgCheckedKeys(checkedKeys as React.Key[])}
                showLine={false}
                showIcon={false}
                style={{ background: 'transparent', color: colorText }}
                titleRender={(nodeData) => (
                  <span
                    style={{
                      color: colorText,
                      fontSize: 14
                    }}
                    title={nodeData.title as string}
                  >
                    {nodeData.title}
                  </span>
                )}
              />
            </ScrollableTreeContainer>
          </div>
        </div>

        {/* 右侧：考场方案机构树 */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <div style={{
            background: darkMode ? '#1e293b' : '#ffffff',
            border: `1px solid ${borderColor}`,
            borderRadius: 8,
            padding: 16,
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 16,
              paddingBottom: 12,
              borderBottom: `1px solid ${borderColor}`
            }}>
              <span style={{ fontSize: 16, fontWeight: 'bold', color: colorText }}>考场方案机构树</span>
              <div style={{ display: 'flex', gap: 8 }}>
                <Button type="primary" size="small" onClick={() => setImportSiteModalOpen(true)}>
                  导入考点方案
                </Button>
                <Button type="primary" size="small" onClick={() => setModalOpen(true)}>
                  + 添加自定义方案
                </Button>
              </div>
            </div>
            <ScrollableTreeContainer
              darkMode={darkMode}
              style={{ flex: 1, height: 'calc(100% - 60px)' }}
            >
              <Tree
                treeData={basicCleanupTreeData}
                checkable
                defaultExpandAll
                checkedKeys={siteCheckedKeys}
                onCheck={(checkedKeys) => setSiteCheckedKeys(checkedKeys as React.Key[])}
                showLine={false}
                showIcon={false}
                style={{ background: 'transparent', color: colorText }}
                titleRender={(nodeData) => (
                  <span
                    style={{
                      color: colorText,
                      fontSize: 14
                    }}
                    title={nodeData.title as string}
                  >
                    {nodeData.title}
                  </span>
                )}
              />
            </ScrollableTreeContainer>
          </div>
        </div>
      </div>
    );
  } else {
    // 二级节点：显示某考点下的考场方案，有考场方案名称列，按钮为导入考场方案和添加自定义考场方案
    const columns = [
      { title: '考方案名称', dataIndex: 'name', key: 'name', align: 'left' as AlignType },
      { title: '考场方案名称', dataIndex: 'plan', key: 'plan', align: 'left' as AlignType },
      { title: '创建时间', dataIndex: 'time', key: 'time', align: 'left' as AlignType },
      { title: '备注', dataIndex: 'remark', key: 'remark', align: 'left' as AlignType },
      { title: '是否显示', dataIndex: 'show', key: 'show', align: 'center' as AlignType, render: (_: any, record: any) => (
        <Switch checked={planShowMap[record.key]} onChange={(checked: boolean) => setPlanShowMap((m: Record<number, boolean>) => ({ ...m, [record.key]: checked }))} />
      ) },
      {
        title: '操作',
        key: 'action',
        align: 'center' as AlignType,
        render: (_: any, record: any) => (
          <>
            <Button type="link">查看</Button>
            {record.type !== 'imported' && <Button type="link">编辑</Button>}
            {record.type === 'imported' &&
              <Button type="link" onClick={() => {
                setRecordForCleanup(record);
                setCleanupStep(0);
                setSelectedCleanupNode([]); // 重置选中状态
                setCleanupModalOpen(true);
              }}>数据清理</Button>
            }
            <Button type="link">统计</Button>
            <Button type="link">导出为静态列表</Button>
            <Button type="link" danger>删除</Button>
          </>
        )
      }
    ];
    pageContent = (
      <>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Button type="primary" style={{ background: '#1890FF', borderRadius: 4, marginRight: 8 }} onClick={() => setImportPlanModalOpen(true)}>导入考场方案</Button>
          <Button type="primary" style={{ background: '#1890FF', borderRadius: 4, marginRight: 8 }} onClick={() => {
            setModalOpen(true);
          }}>+ 添加自定义考场方案</Button>
        </div>
        <Table
          columns={columns}
          dataSource={sitePlanData}
          rowKey="key"
          pagination={false}
          bordered={false}
          style={{ background: bgMain, borderRadius: 8, color: colorText }}
        />
      </>
    );
  }

  return (
    <ConfigProvider theme={getAntdTheme(darkMode)}>
      <div style={{ height: '100vh', background: bgMain, color: colorText }} className={darkMode ? 'dark' : ''}>
        <Layout style={{ height: '100vh', background: bgMain }}>
          <Header style={{
            background: darkMode ? '#232324' : '#ffffff',
            borderBottom: `1px solid ${borderColor}`,
            padding: '0 24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            height: 60
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <h1 style={{ margin: 0, color: colorText, fontSize: 18, fontWeight: 'bold' }}>教育考试网上巡查数据云享平台</h1>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
              <span style={{ color: colorText }}>版本: v1.0.0</span>
              <span style={{ color: colorText }}>admin</span>
            </div>
          </Header>
          <Content style={{ margin: 0, background: 'transparent', padding: 0, display: 'flex', height: 'calc(100vh - 60px)' }}>
            {/* 左侧树状菜单 */}
            <div style={{ width: 260, background: bgMain, borderRight: `1px solid ${borderColor}`, padding: '24px 0 0 0', color: colorText }}>
              <Tree
                treeData={standardTreeData}
                defaultExpandAll
                selectedKeys={[selectedTreeKey]}
                onSelect={(keys: React.Key[]) => setSelectedTreeKey(keys[0] as string)}
                showIcon={false}
                blockNode
                style={{ background: 'transparent', color: colorText, lineHeight: 2.5 }}
              />
            </div>
            {/* 右侧内容区 */}
            <div style={{ flex: 1, background: bgMain, padding: '48px 24px 0 24px', minHeight: 'calc(100vh - 60px)', color: colorText }}>
              {pageContent}
            </div>
          </Content>
        </Layout>

        {/* 导入考点方案 Modal */}
        <Modal
          title={<div
            style={{ width: '100%', cursor: 'move' }}
            onMouseOver={() => { if (disabled) { setDisabled(false); } }}
            onMouseOut={() => { setDisabled(true); }}
          >导入考点方案</div>}
          open={importSiteModalOpen}
          onCancel={handleCloseImportSiteModal}
          footer={isImportingSite ? null : [
              <Button key="cancel" onClick={handleCloseImportSiteModal}>取消</Button>,
              <Button key="import" type="primary" onClick={handleImportSite}>导入</Button>
          ]}
          width={600}
          centered
          bodyStyle={{ paddingTop: 24, minHeight: 250, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          modalRender={modal => <Draggable disabled={disabled} bounds={bounds} nodeRef={draggleRef} onStart={(event, uiData) => onStart(event, uiData)}><div ref={draggleRef}>{modal}</div></Draggable>}
        >
          {isImportingSite ? (
            <div style={{ textAlign: 'center', width: '100%' }}>
              <Progress type="circle" percent={importSiteProgress} />
              <p style={{ marginTop: 20, fontSize: 16 }}>正在导入，请稍候...</p>
            </div>
          ) : (
            <div style={{ display: 'grid', gridTemplateColumns: 'max-content 1fr', gap: '16px 8px', alignItems: 'center', width: '100%' }}>
              <label style={{ textAlign: 'right' }}>
                <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
                考点方案名称:
              </label>
              <Input
                  placeholder="请输入考点方案名称"
                  value={importSiteName}
                  onChange={e => setImportSiteName(e.target.value)}
              />

              <label style={{ textAlign: 'right', alignSelf: 'start', paddingTop: 4 }}>备注:</label>
              <Input.TextArea
                  placeholder="请输入考点方案备注"
                  rows={4}
                  value={importSiteRemark}
                  onChange={e => setImportSiteRemark(e.target.value)}
              />
              <input type="file" ref={fileInputSiteRef} style={{ display: 'none' }} accept=".xlsx" onChange={handleSiteFileSelect} />
              <label style={{ textAlign: 'right' }}>选择考场信息:</label>
              <div style={{ display: 'flex' }}>
                <Input
                  readOnly
                  placeholder="请选择.xlsx格式文件"
                  value={importSiteFilePath}
                  style={{ flex: 1 }}
                />
                <Button style={{ marginLeft: 8 }} onClick={() => fileInputSiteRef.current?.click()}>选择</Button>
              </div>
            </div>
          )}
        </Modal>
        {/* 导入考场方案 Modal */}
        <Modal
          title={<div
            style={{ width: '100%', cursor: 'move' }}
            onMouseOver={() => { if (disabled) { setDisabled(false); } }}
            onMouseOut={() => { setDisabled(true); }}
          >导入考场方案</div>}
          open={importPlanModalOpen}
          onCancel={handleCloseImportPlanModal}
          footer={isImportingPlan ? null : [
              <Button key="cancel" onClick={handleCloseImportPlanModal}>取消</Button>,
              <Button key="import" type="primary" onClick={handleImportPlan}>导入</Button>
          ]}
          width={600}
          centered
          bodyStyle={{ paddingTop: 24, minHeight: 250, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          modalRender={modal => <Draggable disabled={disabled} bounds={bounds} nodeRef={draggleRef} onStart={(event, uiData) => onStart(event, uiData)}><div ref={draggleRef}>{modal}</div></Draggable>}
        >
          {isImportingPlan ? (
            <div style={{ textAlign: 'center', width: '100%' }}>
              <Progress type="circle" percent={importPlanProgress} />
              <p style={{ marginTop: 20, fontSize: 16 }}>正在导入，请稍候...</p>
            </div>
          ) : (
            <div style={{ display: 'grid', gridTemplateColumns: 'max-content 1fr', gap: '16px 8px', alignItems: 'center', width: '100%' }}>
              <label style={{ textAlign: 'right' }}>
                <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
                考场方案名称:
              </label>
              <Input
                  placeholder="请输入考场方案名称"
                  value={importPlanName}
                  onChange={e => setImportPlanName(e.target.value)}
              />

              <label style={{ textAlign: 'right', alignSelf: 'start', paddingTop: 4 }}>备注:</label>
              <Input.TextArea
                  placeholder="请输入考场方案备注"
                  rows={4}
                  value={importPlanRemark}
                  onChange={e => setImportPlanRemark(e.target.value)}
              />
              <input type="file" ref={fileInputPlanRef} style={{ display: 'none' }} accept=".xlsx" onChange={handlePlanFileSelect} />
              <label style={{ textAlign: 'right' }}>选择考场信息:</label>
              <div style={{ display: 'flex' }}>
                <Input
                  readOnly
                  placeholder="请选择.xlsx格式文件"
                  value={importPlanFilePath}
                  style={{ flex: 1 }}
                />
                <Button style={{ marginLeft: 8 }} onClick={() => fileInputPlanRef.current?.click()}>选择</Button>
              </div>
            </div>
          )}
        </Modal>

        {/* 数据清理 Modal */}
        <Modal
          title="清理数据"
          open={cleanupModalOpen}
          onCancel={() => setCleanupModalOpen(false)}
          width={1400}
          footer={null}
          centered
          style={{ top: 20 }}
        >
          <div style={{ padding: 16 }}>
            <Steps current={cleanupStep} style={{ marginBottom: 24 }}>
              <Steps.Step title="脏数据清理" description="自动分析和清理脏数据" />
              <Steps.Step title="拉流清理" description="清理无效的拉流通道" />
              <Steps.Step title="人工清理" description="手动选择需要清理的数据" />
            </Steps>

            {cleanupStep === 0 && (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Button
                    type="primary"
                    loading={dirtyDataAnalyzing}
                    onClick={async () => {
                      setDirtyDataAnalyzing(true);
                      // 模拟分析过程
                      await new Promise(resolve => setTimeout(resolve, 2000));
                      setDirtyDataResultsState(dirtyDataResults);
                      setDirtyDataAnalyzed(true);
                      setDirtyDataAnalyzing(false);
                      message.success('脏数据分析完成');
                    }}
                  >
                    开始分析脏数据
                  </Button>
                  {dirtyDataAnalyzed && (
                    <div style={{ marginLeft: 16, display: 'inline-block' }}>
                      <span>筛选条件：</span>
                      <Select
                        value={dirtyDataFilter}
                        onChange={setDirtyDataFilter}
                        style={{ width: 120, marginLeft: 8 }}
                      >
                        <Select.Option value="all">全部</Select.Option>
                        <Select.Option value="no-exam-site">无对应考点</Select.Option>
                        <Select.Option value="multi-uri">多URI</Select.Option>
                        <Select.Option value="normal">正常</Select.Option>
                      </Select>
                    </div>
                  )}
                </div>

                {dirtyDataAnalyzed && (
                  <Table
                    columns={dirtyDataTableColumns}
                    dataSource={dirtyDataResultsState.filter(item =>
                      dirtyDataFilter === 'all' || item.analysisResult === dirtyDataFilter
                    )}
                    rowKey="key"
                    pagination={{ pageSize: 10 }}
                    scroll={{ x: 1000, y: 400 }}
                    size="small"
                  />
                )}
              </div>
            )}

            {cleanupStep === 1 && (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Button type="primary">开始拉流清理</Button>
                  <span style={{ marginLeft: 16, color: '#666' }}>
                    将清理无效的拉流通道和连接
                  </span>
                </div>
                <div style={{ height: 400, border: '1px solid #d9d9d9', borderRadius: 4, padding: 16 }}>
                  <p>拉流清理功能开发中...</p>
                  <p>此步骤将：</p>
                  <ul>
                    <li>检测无效的拉流连接</li>
                    <li>清理失效的视频通道</li>
                    <li>优化网络连接</li>
                  </ul>
                </div>
              </div>
            )}

            {cleanupStep === 2 && (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Button type="primary">选择清理节点</Button>
                  <span style={{ marginLeft: 16, color: '#666' }}>
                    手动选择需要清理的考场节点
                  </span>
                </div>
                <div style={{ height: 400, border: '1px solid #d9d9d9', borderRadius: 4, padding: 16 }}>
                  <Tree
                    treeData={basicCleanupTreeData}
                    checkable
                    defaultExpandAll
                    checkedKeys={checkedCleanupNodes}
                    onCheck={(checkedKeys) => setCheckedCleanupNodes(checkedKeys as React.Key[])}
                  />
                </div>
              </div>
            )}

            <div style={{ marginTop: 24, textAlign: 'right' }}>
              {cleanupStep > 0 && (
                <Button style={{ marginRight: 8 }} onClick={() => setCleanupStep(cleanupStep - 1)}>
                  上一步
                </Button>
              )}
              {cleanupStep < 2 && (
                <Button type="primary" onClick={() => setCleanupStep(cleanupStep + 1)}>
                  下一步
                </Button>
              )}
              {cleanupStep === 2 && (
                <Button type="primary" onClick={() => {
                  message.success(`已清理 ${checkedCleanupNodes.length} 个节点的数据`);
                  setCleanupModalOpen(false);
                  setCleanupStep(0);
                  setDirtyDataAnalyzed(false);
                  setCheckedCleanupNodes([]);
                }}>
                  完成清理
                </Button>
              )}
            </div>
          </div>
        </Modal>

        {/* 添加自定义考场方案 Modal */}
        <Modal
          title="添加自定义考场方案"
          open={modalOpen}
          onCancel={() => setModalOpen(false)}
          width={1200}
          footer={[
            <Button key="cancel" onClick={() => setModalOpen(false)}>取消</Button>,
            <Button key="save" type="primary" onClick={() => {
              if (!schemeName.trim()) {
                message.error('请输入考场方案名称');
                return;
              }
              message.success('自定义考场方案保存成功');
              setModalOpen(false);
              setSchemeName('');
              setRemark('');
            }}>保存</Button>
          ]}
          centered
        >
          <div style={{ padding: 16 }}>
            <div style={{ marginBottom: 16 }}>
              <label>考场方案名称：</label>
              <Input
                value={schemeName}
                onChange={e => setSchemeName(e.target.value)}
                placeholder="请输入考场方案名称"
                style={{ marginTop: 8 }}
              />
            </div>
            <div style={{ marginBottom: 16 }}>
              <label>备注：</label>
              <Input.TextArea
                value={remark}
                onChange={e => setRemark(e.target.value)}
                placeholder="请输入备注信息"
                rows={3}
                style={{ marginTop: 8 }}
              />
            </div>
            <div>
              <label>考场配置：</label>
              <div style={{
                marginTop: 8,
                height: 400,
                border: '1px solid #d9d9d9',
                borderRadius: 4,
                padding: 16,
                display: 'flex',
                gap: 16
              }}>
                <div style={{ flex: 1 }}>
                  <h4>左侧：实时通道列表</h4>
                  <Tree
                    treeData={basicCleanupTreeData}
                    checkable
                    defaultExpandAll
                    checkedKeys={orgCheckedKeys}
                    onCheck={(checkedKeys) => setOrgCheckedKeys(checkedKeys as React.Key[])}
                    style={{ height: 320, overflowY: 'auto' }}
                  />
                </div>
                <div style={{ flex: 1 }}>
                  <h4>右侧：考场方案机构树</h4>
                  <Tree
                    treeData={basicCleanupTreeData}
                    checkable
                    defaultExpandAll
                    checkedKeys={siteCheckedKeys}
                    onCheck={(checkedKeys) => setSiteCheckedKeys(checkedKeys as React.Key[])}
                    style={{ height: 320, overflowY: 'auto' }}
                  />
                </div>
              </div>
            </div>
          </div>
        </Modal>

        {/* 查看考点方案 Modal */}
        <Modal
          title="查看考点方案"
          open={viewSiteModalOpen}
          onCancel={() => setViewSiteModalOpen(false)}
          width={800}
          footer={[
            <Button key="close" onClick={() => setViewSiteModalOpen(false)}>关闭</Button>
          ]}
          centered
        >
          {currentSiteRecord && (
            <div style={{ padding: 16 }}>
              <div style={{ marginBottom: 16 }}>
                <strong>考点方案名称：</strong>
                <span style={{ marginLeft: 8 }}>{currentSiteRecord.name}</span>
              </div>
              <div style={{ marginBottom: 16 }}>
                <strong>创建时间：</strong>
                <span style={{ marginLeft: 8 }}>{currentSiteRecord.time}</span>
              </div>
              <div style={{ marginBottom: 16 }}>
                <strong>备注：</strong>
                <span style={{ marginLeft: 8 }}>{currentSiteRecord.remark}</span>
              </div>
              <div>
                <strong>考点方案机构树：</strong>
                <div style={{
                  marginTop: 8,
                  padding: 12,
                  border: '1px solid #d9d9d9',
                  borderRadius: 4,
                  background: '#fafafa',
                  height: 400,
                  overflowY: 'auto'
                }}>
                  <Tree
                    treeData={standardTreeData}
                    defaultExpandAll
                    showLine
                    showIcon
                  />
                </div>
              </div>
            </div>
          )}
        </Modal>

        {/* 编辑考点方案 Modal */}
        <Modal
          title="编辑考点方案"
          open={editSiteModalOpen}
          onCancel={() => setEditSiteModalOpen(false)}
          footer={[
            <Button key="cancel" onClick={() => setEditSiteModalOpen(false)}>取消</Button>,
            <Button key="save" type="primary" onClick={() => {
              if (currentSiteRecord) {
                message.success('编辑成功');
                setEditSiteModalOpen(false);
              }
            }}>保存</Button>
          ]}
          width={500}
          centered
        >
          <div style={{ padding: 16 }}>
            <div style={{ marginBottom: 16 }}>
              <label>考点方案名称：</label>
              <Input
                value={editSiteName}
                onChange={e => setEditSiteName(e.target.value)}
                style={{ marginTop: 8 }}
              />
            </div>
            <div>
              <label>备注：</label>
              <Input.TextArea
                value={editSiteRemark}
                onChange={e => setEditSiteRemark(e.target.value)}
                rows={4}
                style={{ marginTop: 8 }}
              />
            </div>
          </div>
        </Modal>

        {/* 删除确认 Modal */}
        <Modal
          title="确认删除"
          open={deleteSiteModalOpen}
          onCancel={() => setDeleteSiteModalOpen(false)}
          footer={[
            <Button key="cancel" onClick={() => setDeleteSiteModalOpen(false)}>取消</Button>,
            <Button key="delete" type="primary" danger onClick={() => {
              if (currentSiteRecord) {
                message.success('删除成功');
                setDeleteSiteModalOpen(false);
              }
            }}>确认删除</Button>
          ]}
          centered
        >
          <p>确定要删除考点方案 "{currentSiteRecord?.name}" 吗？此操作不可恢复。</p>
        </Modal>

        {/* 暗黑表格样式和数据清理样式 */}
        <style>{`
          .dark .ant-table, .dark .ant-table-container, .dark .ant-table-content {
            background: #18191A !important;
          }
          .dark .ant-table-thead > tr > th {
            background: #232324 !important;
            color: #fff !important;
          }
          .dark .ant-table-tbody > tr > td {
            background: #18191A !important;
            color: #fff !important;
          }

          /* 数据清理Modal样式 */
          .ant-modal-content {
            overflow: hidden;
          }

          /* 树组件样式优化 */
          .ant-tree .ant-tree-node-content-wrapper {
            padding: 2px 4px;
            border-radius: 4px;
            transition: all 0.2s;
          }

          .ant-tree .ant-tree-node-content-wrapper:hover {
            background-color: rgba(24, 144, 255, 0.1) !important;
          }

          .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
            background-color: rgba(24, 144, 255, 0.2) !important;
          }

          /* 表格行悬停效果 */
          .ant-table-tbody > tr:hover > td {
            background-color: rgba(24, 144, 255, 0.05) !important;
          }

          .dark .ant-table-tbody > tr:hover > td {
            background-color: rgba(24, 144, 255, 0.1) !important;
          }

          /* 滚动条样式 */
          .ant-table-body::-webkit-scrollbar {
            width: 8px;
            height: 8px;
          }

          .ant-table-body::-webkit-scrollbar-track {
            background: transparent;
          }

          .ant-table-body::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
          }

          .dark .ant-table-body::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
          }
        `}</style>
      </div>
    </ConfigProvider>
  );
};

export default AIListManagement 