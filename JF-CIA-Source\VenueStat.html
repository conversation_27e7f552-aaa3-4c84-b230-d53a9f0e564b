<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教育考点统计系统</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    <style>
        /* 全局样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            display: flex;
            min-height: 100vh;
        }

        /* 左侧树形结构 */
        .tree-container {
            width: 300px;
            background-color: #2d2d2d;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #3d3d3d;
        }

        /* 右侧统计区域 */
        .statistics-container {
            flex: 1;
            padding: 20px;
            background-color: #1a1a1a;
            overflow-y: auto;
        }

        /* 树形结构样式 */
        .tree {
            list-style: none;
            padding-left: 0;
        }

        .tree li {
            margin: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .tree-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .tree-item:hover {
            background-color: #3d3d3d;
        }

        .tree-item.active {
            background-color: #4a4a4a;
        }

        /* 展开/折叠图标 */
        .toggle {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #666;
            border-radius: 3px;
            cursor: pointer;
        }

        /* 统计区域样式 */
        .statistics-card {
            background-color: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .statistics-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: #fff;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #3d3d3d;
        }

        .data-table th {
            background-color: #3d3d3d;
            color: #fff;
        }

        .data-table tbody tr:hover {
            background-color: #3d3d3d;
        }

        /* 图表容器 */
        #chartContainer {
            width: 100%;
            height: 400px;
            margin-top: 20px;
        }

        /* 隐藏子树 */
        .tree ul {
            display: none;
        }

        /* 展开时显示子树 */
        .tree .expanded > ul {
            display: block;
        }

        /* 展开状态下的图标 */
        .expanded > .tree-item .toggle {
            background-color: #4a4a4a;
        }

        /* 导出按钮样式 */
        .export-button {
            background-color: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 0;
            transition: background-color 0.3s;
        }

        .export-button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <!-- 左侧树形结构 -->
    <div class="tree-container">
        <ul class="tree">
            <li class="expanded">
                <div class="tree-item" data-id="root">
                    <span class="toggle">-</span>
                    <span class="icon">📁</span>
                    <span class="name">国家教育部</span>
                </div>
                <ul>
                    <li>
                        <div class="tree-item" data-id="sc">
                            <span class="toggle">+</span>
                            四川省
                        </div>
                        <ul>
                            <li>
                                <div class="tree-item" data-id="cd">
                                    <span class="toggle">+</span>
                                    成都市
                                </div>
                                <ul>
                                    <li><div class="tree-item" data-id="cd_jj">锦江区</div></li>
                                    <li><div class="tree-item" data-id="cd_qy">青羊区</div></li>
                                    <li><div class="tree-item" data-id="cd_jn">金牛区</div></li>
                                    <li><div class="tree-item" data-id="cd_wh">武侯区</div></li>
                                    <li><div class="tree-item" data-id="cd_ch">成华区</div></li>
                                </ul>
                            </li>
                            <li>
                                <div class="tree-item" data-id="my">
                                    <span class="toggle">+</span>
                                    绵阳市
                                </div>
                                <ul>
                                    <li><div class="tree-item" data-id="my_fc">涪城区</div></li>
                                    <li><div class="tree-item" data-id="my_yx">游仙区</div></li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <div class="tree-item" data-id="js">
                            <span class="toggle">+</span>
                            江苏省
                        </div>
                        <ul>
                            <li>
                                <div class="tree-item" data-id="nj">
                                    <span class="toggle">+</span>
                                    南京市
                                </div>
                                <ul>
                                    <li><div class="tree-item" data-id="nj_xw">玄武区</div></li>
                                    <li><div class="tree-item" data-id="nj_qh">秦淮区</div></li>
                                    <li><div class="tree-item" data-id="nj_jy">建邺区</div></li>
                                    <li><div class="tree-item" data-id="nj_gl">鼓楼区</div></li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                    <!-- 其他省份数据 -->
                    <li>
                        <div class="tree-item" data-id="gd">
                            <span class="toggle">+</span>
                            广东省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="zj">
                            <span class="toggle">+</span>
                            浙江省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="hub">
                            <span class="toggle">+</span>
                            湖北省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="hun">
                            <span class="toggle">+</span>
                            湖南省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="jx">
                            <span class="toggle">+</span>
                            江西省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="gz">
                            <span class="toggle">+</span>
                            贵州省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="hen">
                            <span class="toggle">+</span>
                            河南省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="ln">
                            <span class="toggle">+</span>
                            辽宁省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="hlj">
                            <span class="toggle">+</span>
                            黑龙江省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="jl">
                            <span class="toggle">+</span>
                            吉林省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="heb">
                            <span class="toggle">+</span>
                            河北省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="fj">
                            <span class="toggle">+</span>
                            福建省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="sd">
                            <span class="toggle">+</span>
                            山东省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="ah">
                            <span class="toggle">+</span>
                            安徽省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="sx">
                            <span class="toggle">+</span>
                            山西省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="shx">
                            <span class="toggle">+</span>
                            陕西省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="gs">
                            <span class="toggle">+</span>
                            甘肃省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="qh">
                            <span class="toggle">+</span>
                            青海省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="yn">
                            <span class="toggle">+</span>
                            云南省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="gx">
                            <span class="toggle">+</span>
                            广西壮族自治区
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="nmg">
                            <span class="toggle">+</span>
                            内蒙古自治区
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="xz">
                            <span class="toggle">+</span>
                            西藏自治区
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="nx">
                            <span class="toggle">+</span>
                            宁夏回族自治区
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="xj">
                            <span class="toggle">+</span>
                            新疆维吾尔自治区
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="han">
                            <span class="toggle">+</span>
                            海南省
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="bj">
                            <span class="toggle">+</span>
                            北京市
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="tj">
                            <span class="toggle">+</span>
                            天津市
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="sh">
                            <span class="toggle">+</span>
                            上海市
                        </div>
                    </li>
                    <li>
                        <div class="tree-item" data-id="cq">
                            <span class="toggle">+</span>
                            重庆市
                        </div>
                    </li>
                </ul>
            </li>
        </ul>
    </div>

    <!-- 右侧统计区域 -->
    <div class="statistics-container">
        <div class="statistics-card">
            <h2 class="statistics-title">考场数量统计图表</h2>
            <button class="export-button" id="exportData">导出统计数据</button>
            <div id="chartContainer"></div>
        </div>

        <div class="statistics-card">
            <h2 class="statistics-title">考场数量统计表格</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>地区名称</th>
                        <th>考场数量</th>
                        <th>占比</th>
                    </tr>
                </thead>
                <tbody id="statsTableBody">
                    <!-- 数据将通过 JavaScript 动态填充 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockData = {
            root: {
                '四川省': 3120,
                '江苏省': 2860,
                '浙江省': 2560,
                '广东省': 3360,
                '湖北省': 1960,
                '湖南省': 2240,
                '江西省': 1720,
                '贵州省': 1840,
                '河南省': 2900,
                '辽宁省': 2160,
                '黑龙江省': 1780,
                '吉林省': 1520,
                '河北省': 2640,
                '福建省': 1920,
                '山东省': 3160,
                '安徽省': 2040,
                '山西省': 1760,
                '陕西省': 1880,
                '甘肃省': 1640,
                '青海省': 900,
                '云南省': 1780,
                '广西壮族自治区': 1900,
                '内蒙古自治区': 1560,
                '西藏自治区': 840,
                '宁夏回族自治区': 760,
                '新疆维吾尔自治区': 1700,
                '海南省': 920,
                '北京市': 1360,
                '天津市': 1040,
                '上海市': 1440,
                '重庆市': 1280
            },
            sc: {
                '成都市': 900,
                '绵阳市': 560,
                '德阳市': 440,
                '乐山市': 360,
                '宜宾市': 500,
                '泸州市': 320,
                '内江市': 240,
                '自贡市': 280,
                '攀枝花市': 160,
                '广元市': 200,
                '遂宁市': 240,
                '南充市': 360,
                '眉山市': 280,
                '广安市': 240,
                '达州市': 320,
                '雅安市': 200,
                '巴中市': 160,
                '资阳市': 240,
                '阿坝藏族羌族自治州': 160,
                '甘孜藏族自治州': 120,
                '凉山彝族自治州': 200
            },
            cd: {
                '锦江区': 160,
                '青羊区': 120,
                '金牛区': 140,
                '武侯区': 160,
                '成华区': 120,
                '龙泉驿区': 80,
                '青白江区': 60,
                '新都区': 80,
                '温江区': 60,
                '双流区': 100,
                '郫都区': 80,
                '新津区': 60
            },
            js: {
                '南京市': 760,
                '苏州市': 700,
                '无锡市': 560,
                '常州市': 440,
                '镇江市': 400,
                '南通市': 500,
                '扬州市': 360,
                '泰州市': 320,
                '盐城市': 400,
                '徐州市': 520,
                '淮安市': 300,
                '连云港市': 280,
                '宿迁市': 240
            },
            nj: {
                '玄武区': 100,
                '秦淮区': 120,
                '建邺区': 80,
                '鼓楼区': 100,
                '浦口区': 80,
                '栖霞区': 80,
                '雨花台区': 60,
                '江宁区': 100,
                '六合区': 60,
                '溧水区': 40,
                '高淳区': 40
            },
            // 区县级考场数据
            cd_jj: {
                '锦江区第一考场': 30,
                '锦江区第二考场': 25,
                '锦江区第三考场': 35,
                '锦江区第四考场': 40,
                '锦江区第五考场': 30
            },
            cd_qy: {
                '青羊区第一考场': 25,
                '青羊区第二考场': 30,
                '青羊区第三考场': 35,
                '青羊区第四考场': 30
            },
            cd_jn: {
                '金牛区第一考场': 35,
                '金牛区第二考场': 30,
                '金牛区第三考场': 40,
                '金牛区第四考场': 35
            },
            nj_xw: {
                '玄武区第一考场': 25,
                '玄武区第二考场': 30,
                '玄武区第三考场': 25,
                '玄武区第四考场': 20
            },
            nj_qh: {
                '秦淮区第一考场': 30,
                '秦淮区第二考场': 35,
                '秦淮区第三考场': 25,
                '秦淮区第四考场': 30
            }
        };

        // 初始化图表
        const chartDom = document.getElementById('chartContainer');
        const myChart = echarts.init(chartDom, 'dark');

        // 导出功能
        function exportToExcel(data, fileName) {
            // 创建工作簿
            const wb = XLSX.utils.book_new();
            
            // 创建工作表数据
            const wsData = [
                ['地区名称', '考场数量', '占比'],
                ...Object.entries(data).map(([name, value]) => [
                    name,
                    value,
                    `${((value / Object.values(data).reduce((a, b) => a + b, 0)) * 100).toFixed(1)}%`
                ])
            ];
            
            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            
            // 将工作表添加到工作簿
            XLSX.utils.book_append_sheet(wb, ws, "统计数据");
            
            // 导出文件
            XLSX.writeFile(wb, `${fileName}.xlsx`);
        }

        let currentNodeId = 'root';

        // 更新统计数据
        function updateStats(nodeId) {
            currentNodeId = nodeId;
            const data = mockData[nodeId] || {};
            const total = Object.values(data).reduce((a, b) => a + b, 0);
            
            // 更新表格
            const tbody = document.getElementById('statsTableBody');
            tbody.innerHTML = Object.entries(data)
                .map(([name, value]) => `
                    <tr>
                        <td>${name}</td>
                        <td>${value}</td>
                        <td>${((value / total) * 100).toFixed(1)}%</td>
                    </tr>
                `).join('');

            // 更新图表
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: Object.keys(data),
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '考场数量'
                },
                series: [
                    {
                        name: '考场数量',
                        type: 'bar',
                        data: Object.values(data),
                        itemStyle: {
                            color: '#91cc75'
                        },
                        label: {
                            show: true,
                            position: 'top',
                            color: '#fff',
                            fontSize: 12
                        },
                        barWidth: '40%'
                    }
                ]
            };

            myChart.setOption(option);
        }

        // 树形结构交互
        document.querySelectorAll('.tree-item').forEach(item => {
            const toggle = item.querySelector('.toggle');
            if (toggle) {
                toggle.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const li = item.parentElement;
                    li.classList.toggle('expanded');
                    toggle.textContent = li.classList.contains('expanded') ? '-' : '+';
                });
            }

            item.addEventListener('click', () => {
                // 移除其他活动项的高亮
                document.querySelectorAll('.tree-item.active').forEach(active => {
                    active.classList.remove('active');
                });
                
                // 添加当前项的高亮
                item.classList.add('active');

                // 更新统计数据
                const nodeId = item.getAttribute('data-id');
                if (nodeId) {
                    updateStats(nodeId);
                }
            });
        });

        // 导出数据
        document.getElementById('exportData').addEventListener('click', () => {
            const currentData = mockData[currentNodeId] || {};
            exportToExcel(currentData, '考场统计数据');
        });

        // 初始化显示国家级数据
        updateStats('root');

        // 监听窗口大小变化，调整图表大小
        window.addEventListener('resize', () => {
            myChart.resize();
        });
    </script>
</body>
</html> 