import { create } from 'zustand'
import { theme } from 'antd'

interface ThemeState {
  darkMode: boolean
  toggle: () => void
  setDarkMode: (dark: boolean) => void
}

// 使用 zustand 管理主题状态
export const useThemeStore = create<ThemeState>((set, get) => ({
  darkMode: true, // 默认暗黑模式
  toggle: () => {
    const newDarkMode = !get().darkMode
    set({ darkMode: newDarkMode })
    // 同步更新 HTML class
    if (newDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  },
  setDarkMode: (dark: boolean) => {
    set({ darkMode: dark })
    // 同步更新 HTML class
    if (dark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  },
}))

export const getAntdTheme = (darkMode: boolean) => ({
  algorithm: darkMode ? theme.darkAlgorithm : theme.defaultAlgorithm
})
